('C:\\Users\\<USER>\\Desktop\\Python\\Tools\\dist\\Ticket Process.exe',
 <PERSON><PERSON>e,
 False,
 False,
 'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket Process\\Ticket '
 'Process.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_kivy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_kivy.py',
   'PYSOURCE'),
  ('pyi_rth_gstreamer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_gstreamer.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('Ticket Process',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\Ticket Process.py',
   'PYSOURCE'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python311.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\python311.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\scissor_instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\scissor_instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\boxshadow.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\boxshadow.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\fbo.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\fbo.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\gl_instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\gl_instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\stencil_instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\stencil_instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\vertex_instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\vertex_instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\context_instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\context_instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\instructions.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\instructions.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\context.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\context.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\image\\_img_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\_img_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_imagingft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\transformation.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\transformation.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\texture.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\texture.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\text\\text_layout.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_layout.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32file.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32file.pyd',
   'EXTENSION'),
  ('kivy\\weakproxy.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\weakproxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\properties.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\_event.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\_event.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl_backend\\cgl_gl.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\cgl_gl.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\text\\_text_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\_text_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\buffer.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\buffer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\vbo.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\vbo.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\lib\\gstplayer\\_gstplayer.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\gstplayer\\_gstplayer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\opengl.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\opengl.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\svg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\svg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\vertex.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\vertex.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\window\\window_info.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_info.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl_backend\\cgl_debug.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\cgl_debug.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\tesselator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\tesselator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\clipboard\\_clipboard_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\_clipboard_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\audio\\audio_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\compiler.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\compiler.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl_backend\\cgl_glew.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\cgl_glew.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl_backend\\cgl_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\cgl_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\core\\window\\_window_sdl2.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\_window_sdl2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\opengl_utils.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\opengl_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\shader.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\shader.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\graphics\\cgl_backend\\cgl_mock.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\cgl_mock.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\_clock.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\_clock.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('pyodbc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyodbc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kivy\\_metrics.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\_metrics.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('SDL2.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\share\\sdl2\\bin\\SDL2.dll',
   'BINARY'),
  ('SDL2_image.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\share\\sdl2\\bin\\SDL2_image.dll',
   'BINARY'),
  ('glew32.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\share\\glew\\bin\\glew32.dll',
   'BINARY'),
  ('SDL2_ttf.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\share\\sdl2\\bin\\SDL2_ttf.dll',
   'BINARY'),
  ('SDL2_mixer.dll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\share\\sdl2\\bin\\SDL2_mixer.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\python3.dll',
   'BINARY'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('kivy_install\\data\\keyboards\\qwerty.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\qwerty.json',
   'DATA'),
  ('kivy_install\\data\\fonts\\DejaVuSans.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\DejaVuSans.ttf',
   'DATA'),
  ('kivy_install\\modules\\webdebugger.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\webdebugger.py',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\screen.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\screen.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\webdebugger.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\webdebugger.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\images\\testpattern.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\testpattern.png',
   'DATA'),
  ('kivy_install\\data\\images\\cursor.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\cursor.png',
   'DATA'),
  ('kivy_install\\modules\\inspector.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\inspector.py',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-24.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-24.png',
   'DATA'),
  ('kivy_install\\modules\\console.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\console.py',
   'DATA'),
  ('kivy_install\\modules\\touchring.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\touchring.py',
   'DATA'),
  ('kivy_install\\modules\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__init__.py',
   'DATA'),
  ('kivy_install\\data\\keyboards\\en_US.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\en_US.json',
   'DATA'),
  ('kivy_install\\data\\fonts\\Roboto-Regular.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\Roboto-Regular.ttf',
   'DATA'),
  ('kivy_install\\data\\glsl\\header.fs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\glsl\\header.fs',
   'DATA'),
  ('kivy_install\\data\\glsl\\default.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\glsl\\default.png',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-512.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-512.png',
   'DATA'),
  ('kivy_install\\data\\keyboards\\fr_CH.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\fr_CH.json',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-128.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-128.png',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\inspector.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\inspector.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\screen.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\screen.py',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\joycursor.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\joycursor.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\fonts\\RobotoMono-Regular.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\RobotoMono-Regular.ttf',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\touchring.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\touchring.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\images\\defaulttheme.atlas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\defaulttheme.atlas',
   'DATA'),
  ('kivy_install\\data\\keyboards\\de.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\de.json',
   'DATA'),
  ('kivy_install\\data\\fonts\\Roboto-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\Roboto-Bold.ttf',
   'DATA'),
  ('kivy_install\\modules\\joycursor.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\joycursor.py',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\showborder.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\showborder.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\monitor.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\monitor.py',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-16.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-16.png',
   'DATA'),
  ('kivy_install\\data\\glsl\\default.fs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\glsl\\default.fs',
   'DATA'),
  ('kivy_install\\data\\images\\image-loading.gif',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\image-loading.gif',
   'DATA'),
  ('kivy_install\\modules\\cursor.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\cursor.py',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\console.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\console.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\cursor.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\cursor.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\showborder.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\showborder.py',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-48.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-48.png',
   'DATA'),
  ('kivy_install\\data\\glsl\\header.vs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\glsl\\header.vs',
   'DATA'),
  ('kivy_install\\data\\glsl\\default.vs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\glsl\\default.vs',
   'DATA'),
  ('kivy_install\\data\\keyboards\\qwertz.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\qwertz.json',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-32.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-32.png',
   'DATA'),
  ('kivy_install\\data\\settings_kivy.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\settings_kivy.json',
   'DATA'),
  ('kivy_install\\data\\images\\defaultshape.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\defaultshape.png',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\_webdebugger.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\_webdebugger.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\style.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\style.kv',
   'DATA'),
  ('kivy_install\\data\\keyboards\\de_CH.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\de_CH.json',
   'DATA'),
  ('kivy_install\\data\\keyboards\\es_ES.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\es_ES.json',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-64.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-64.png',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\monitor.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\monitor.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\fonts\\Roboto-BoldItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\Roboto-BoldItalic.ttf',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\recorder.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\recorder.cpython-311.pyc',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-256.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-256.png',
   'DATA'),
  ('kivy_install\\data\\images\\defaulttheme-0.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\defaulttheme-0.png',
   'DATA'),
  ('kivy_install\\data\\keyboards\\azerty.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\keyboards\\azerty.json',
   'DATA'),
  ('kivy_install\\data\\logo\\kivy-icon-64.ico',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\logo\\kivy-icon-64.ico',
   'DATA'),
  ('kivy_install\\modules\\keybinding.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\keybinding.py',
   'DATA'),
  ('kivy_install\\modules\\recorder.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\recorder.py',
   'DATA'),
  ('kivy_install\\data\\images\\image-loading.zip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\image-loading.zip',
   'DATA'),
  ('kivy_install\\modules\\_webdebugger.py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\_webdebugger.py',
   'DATA'),
  ('kivy_install\\data\\images\\background.jpg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\images\\background.jpg',
   'DATA'),
  ('kivy_install\\data\\fonts\\Roboto-Italic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\data\\fonts\\Roboto-Italic.ttf',
   'DATA'),
  ('kivy_install\\modules\\__pycache__\\keybinding.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__pycache__\\keybinding.cpython-311.pyc',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Light.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Light.ttf',
   'DATA'),
  ('kivymd\\uix\\chip\\chip.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\chip\\chip.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Thin.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Thin.ttf',
   'DATA'),
  ('kivymd\\uix\\card\\card.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\card\\card.kv',
   'DATA'),
  ('kivymd\\uix\\dialog\\dialog.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dialog\\dialog.kv',
   'DATA'),
  ('kivymd\\images\\red.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\red.png',
   'DATA'),
  ('kivymd\\images\\green.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\green.png',
   'DATA'),
  ('kivymd\\uix\\bottomsheet\\bottomsheet.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomsheet\\bottomsheet.kv',
   'DATA'),
  ('kivymd\\uix\\menu\\menu.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\menu\\menu.kv',
   'DATA'),
  ('kivymd\\uix\\textfield\\textfield.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\textfield\\textfield.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-BoldItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-BoldItalic.ttf',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Italic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Italic.ttf',
   'DATA'),
  ('kivymd\\fonts\\Roboto-LightItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-LightItalic.ttf',
   'DATA'),
  ('kivymd\\uix\\tooltip\\tooltip.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tooltip\\tooltip.kv',
   'DATA'),
  ('kivymd\\fonts\\materialdesignicons-webfont.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\materialdesignicons-webfont.ttf',
   'DATA'),
  ('kivymd\\images\\alpha_layer.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\alpha_layer.png',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Medium.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Medium.ttf',
   'DATA'),
  ('kivymd\\uix\\filemanager\\filemanager.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\filemanager\\filemanager.kv',
   'DATA'),
  ('kivymd\\uix\\progressbar\\progressbar.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\progressbar\\progressbar.kv',
   'DATA'),
  ('kivymd\\uix\\datatables\\datatables.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\datatables\\datatables.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-ThinItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-ThinItalic.ttf',
   'DATA'),
  ('kivymd\\uix\\expansionpanel\\expansionpanel.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\expansionpanel\\expansionpanel.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-MediumItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-MediumItalic.ttf',
   'DATA'),
  ('kivymd\\uix\\tab\\tab.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tab\\tab.kv',
   'DATA'),
  ('kivymd\\uix\\selectioncontrol\\selectioncontrol.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selectioncontrol\\selectioncontrol.kv',
   'DATA'),
  ('kivymd\\images\\folder.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\folder.png',
   'DATA'),
  ('kivymd\\images\\logo\\kivymd-icon-128.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\logo\\kivymd-icon-128.png',
   'DATA'),
  ('kivymd\\uix\\spinner\\spinner.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\spinner\\spinner.kv',
   'DATA'),
  ('kivymd\\uix\\swiper\\swiper.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\swiper\\swiper.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-BlackItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-BlackItalic.ttf',
   'DATA'),
  ('kivymd\\images\\logo\\kivymd-icon-256.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\logo\\kivymd-icon-256.png',
   'DATA'),
  ('kivymd\\images\\black.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\black.png',
   'DATA'),
  ('kivymd\\uix\\button\\button.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\button\\button.kv',
   'DATA'),
  ('kivymd\\images\\yellow.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\yellow.png',
   'DATA'),
  ('kivymd\\uix\\label\\label.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\label\\label.kv',
   'DATA'),
  ('kivymd\\images\\blue.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\blue.png',
   'DATA'),
  ('kivymd\\images\\transparent.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\transparent.png',
   'DATA'),
  ('kivymd\\uix\\pickers\\timepicker\\timepicker.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\pickers\\timepicker\\timepicker.kv',
   'DATA'),
  ('kivymd\\uix\\refreshlayout\\refreshlayout.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\refreshlayout\\refreshlayout.kv',
   'DATA'),
  ('kivymd\\uix\\dropdownitem\\dropdownitem.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dropdownitem\\dropdownitem.kv',
   'DATA'),
  ('kivymd\\uix\\sliverappbar\\sliverappbar.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\sliverappbar\\sliverappbar.kv',
   'DATA'),
  ('kivymd\\uix\\pickers\\datepicker\\datepicker.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\pickers\\datepicker\\datepicker.kv',
   'DATA'),
  ('kivymd\\uix\\segmentedbutton\\segmentedbutton.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedbutton\\segmentedbutton.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Regular.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Regular.ttf',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Bold.ttf',
   'DATA'),
  ('kivymd\\uix\\toolbar\\toolbar.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\toolbar\\toolbar.kv',
   'DATA'),
  ('kivymd\\uix\\navigationdrawer\\navigationdrawer.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationdrawer\\navigationdrawer.kv',
   'DATA'),
  ('kivymd\\uix\\selection\\selection.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selection\\selection.kv',
   'DATA'),
  ('kivymd\\images\\logo\\kivymd-icon-512.png',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\images\\logo\\kivymd-icon-512.png',
   'DATA'),
  ('kivymd\\uix\\backdrop\\backdrop.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\backdrop\\backdrop.kv',
   'DATA'),
  ('kivymd\\uix\\imagelist\\imagelist.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\imagelist\\imagelist.kv',
   'DATA'),
  ('kivymd\\uix\\slider\\slider.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\slider\\slider.kv',
   'DATA'),
  ('kivymd\\uix\\list\\list.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\list\\list.kv',
   'DATA'),
  ('kivymd\\uix\\pickers\\colorpicker\\colorpicker.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\pickers\\colorpicker\\colorpicker.kv',
   'DATA'),
  ('kivymd\\uix\\navigationrail\\navigationrail.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationrail\\navigationrail.kv',
   'DATA'),
  ('kivymd\\fonts\\Roboto-Black.ttf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\fonts\\Roboto-Black.ttf',
   'DATA'),
  ('kivymd\\uix\\banner\\banner.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\banner\\banner.kv',
   'DATA'),
  ('kivymd\\uix\\bottomnavigation\\bottomnavigation.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomnavigation\\bottomnavigation.kv',
   'DATA'),
  ('kivymd\\uix\\snackbar\\snackbar.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\snackbar\\snackbar.kv',
   'DATA'),
  ('kivymd\\uix\\segmentedcontrol\\segmentedcontrol.kv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedcontrol\\segmentedcontrol.kv',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\framing.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\framing.css',
   'DATA'),
  ('docutils\\writers\\pep_html\\template.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pep_html\\template.txt',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\minimal.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\minimal.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\mmlextra.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\mmlextra.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamsa.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamsa.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\xhtml1-symbol.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\xhtml1-symbol.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomopf.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomopf.txt',
   'DATA'),
  ('docutils\\writers\\odf_odt\\styles.odt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\styles.odt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\s5-core.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\s5-core.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isocyr2.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isocyr2.txt',
   'DATA'),
  ('docutils\\writers\\pep_html\\pep.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pep_html\\pep.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomopf-wide.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomopf-wide.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isogrk4.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isogrk4.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\medium-white\\framing.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\medium-white\\framing.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamsr.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamsr.txt',
   'DATA'),
  ('docutils\\writers\\latex2e\\titlingpage.tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\titlingpage.tex',
   'DATA'),
  ('docutils\\writers\\latex2e\\docutils.sty',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\docutils.sty',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\xhtml1-special.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\xhtml1-special.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isobox.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isobox.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isopub.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isopub.txt',
   'DATA'),
  ('docutils\\writers\\latex2e\\titlepage.tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\titlepage.tex',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\responsive.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\responsive.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\xhtml1-lat1.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\xhtml1-lat1.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isogrk1.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isogrk1.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\pretty.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomfrk-wide.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomfrk-wide.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomscr.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomscr.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\print.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\print.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomfrk.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomfrk.txt',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\template.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\template.txt',
   'DATA'),
  ('docutils\\writers\\latex2e\\default.tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\default.tex',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamso.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamso.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\slides.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\slides.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isolat1.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isolat1.txt',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\tuftig.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\tuftig.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\small-black\\__base__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\small-black\\__base__',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\mmlextra-wide.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\mmlextra-wide.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamsn.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamsn.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\small-black\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\small-black\\pretty.css',
   'DATA'),
  ('docutils\\writers\\html4css1\\template.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html4css1\\template.txt',
   'DATA'),
  ('docutils\\writers\\html4css1\\html4css1.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html4css1\\html4css1.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isogrk2.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isogrk2.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isodia.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isodia.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\s5defs.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\s5defs.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isonum.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isonum.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\big-black\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\big-black\\pretty.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\mmlalias.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\mmlalias.txt',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\italic-field-names.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\italic-field-names.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isotech.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isotech.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamsc.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamsc.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\outline.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\outline.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\medium-black\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\medium-black\\pretty.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\big-black\\__base__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\big-black\\__base__',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\big-white\\framing.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\big-white\\framing.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isomscr-wide.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isomscr-wide.txt',
   'DATA'),
  ('docutils\\docutils.conf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\docutils.conf',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\medium-white\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\medium-white\\pretty.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\slides.js',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\slides.js',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\big-black\\framing.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\big-black\\framing.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\README.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isolat2.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isolat2.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\big-white\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\big-white\\pretty.css',
   'DATA'),
  ('docutils\\writers\\latex2e\\xelatex.tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\xelatex.tex',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isogrk3.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isogrk3.txt',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isogrk4-wide.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isogrk4-wide.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\small-white\\framing.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\small-white\\framing.css',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\plain.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\plain.css',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isoamsb.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isoamsb.txt',
   'DATA'),
  ('docutils\\writers\\html5_polyglot\\math.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\math.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\default\\opera.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\default\\opera.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\medium-black\\__base__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\medium-black\\__base__',
   'DATA'),
  ('docutils\\parsers\\rst\\include\\isocyr1.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\include\\isocyr1.txt',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\small-white\\pretty.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\small-white\\pretty.css',
   'DATA'),
  ('docutils\\writers\\s5_html\\themes\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\themes\\README.txt',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\LICENSE',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
   'Process\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\python311.dll')
