# -*- coding: utf-8 -*-
import sys
import os
import json
import pickle
import atexit
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import pyodbc
import datetime
import getpass
import pandas as pd
import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
import time
import unicodedata
import pyperclip

# Ensure UTF-8 encoding for all text operations
if sys.version_info[0] >= 3:
    try:
        import io
        if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.buffer:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        pass  # Skip if already configured or not available

# Set CustomTkinter appearance
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Hardcoded blurb-to-issue mapping
blurbs_dict = {
    "Season Missing": "Season is missing on the EDP/Detail Page",
    "Missing Offers/Avails": "Offers/Avails are missing in EDP/CRMS",
    "Episode Missing": "Episode is missing in EDP/Detail Page",
    "Expired Offers/Avails": "Offers/Avails have expired in EDP/CRMS",
    "Merge Issue": "Title requires merging in the catalog",
    "Unmerge Issue": "Incorrectly merged across vendors or Seasons merged incorrectly, need separate metadata pages",
    "Tech Issue": "Technical issue observed, further investigation needed",
    "Offers Takendown": "Offers have been taken down in EDP",
    "VCID Error": "Episode is missing on the site due to a VCID error",
    "Pricing Issue": "Play/Buy button is missing on the detail page due to a pricing issue",
    "Orphaned Season/Episode": "The Season/Episode are not aligned within the Season/Episode hierarchy",
    "Offer Creations": "Live avails exist, but the offer hasn't been created in EDP",
    "Play Button Missing": "Play/Buy button is missing on the Detail Page",
}

# RCA options mapping
rca_dict = {
    "Future Start Date (Prime)" : "Episode X is missing/unavailable (play button missing) due to a future avail start-date. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Expired Prime Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (No Deal ID)" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (With Deal ID)" : " The title is missing prime avails. Looked at Acquire to verify the <dealID> and found that, i.  is still in window, ii.  has the missing title and impacted territories as part of the deal",
    "Future Start Date (TVOD or Channel)" : "Episode X is missing/unavailable due to a future avail start-date. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Expired TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Missing TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Season Missing (Prime)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (Prime)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Season Missing (TVOD or Channel)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (TVOD or Channel)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Incorrect Sequence" : "Episode X appears to be missing because the episode sequence in avails does not match with episode sequence in metadata. Please confirm which episode sequence is correct and update accordingly.",
    "Merge Issue (1C issue)" : "There are two versions of this title suggesting likely a 1C issue. Version 1 with (insert URN from EDP page 1) and Version 2 with (insert URN from EDP page2). Version 2 is not live and is garnering customer impressions meaning customers are exposed to the wrong version. Requesting 1C team to execute a merge.",
    "Non-Actionable Right Scope" : "Episode X is missing/unavailable due to a non-actionable right scope. Please review if the scope status needs to be updated to make the title live to customers.",
    "Offer Issue (PVD Workflow)" : "This title has in-window avails however offers are not created. We've validated that 'contributing user' is 'pvd-workflow'. Please review whether offers can be created so that this title is made available to customers.",
    "Offer Issue (Non - PVD Workflow)" : "This title has in-window avails however offers are not created. We've validated that this offer is not tied to split-licensing or PVD or season-offer/pass issues. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Split License)" : "This title has in-window avails, however offers are not created. We've validated that this offer is tied to split-licensing. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Season Page)" : "Season has in-window avails however season-offers / season pass is not live. Please review whether offers need to be created so that season pass/offer is made available to customers.",
    "Offer Issue (Season Pass )" : "This season has in-window avails however season-offers / season pass is not approved. Please review whether offer can be approved so that season pass/offer is made available to customers.",
    "Offer Issue ('Not Approved' or 'Taken Down')" : "This title has in-window avails however offers are not approved. We've validated that 'contributing user' is not 'pvd-workflow' and release_status = 'released' on CST. Please review whether offers need to be approved so that this title is made available to customers.",
    "Mezz Missing" : "This title has in-window avails however offers are not approved. This is due to following failing rules <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers.",
    "Offer Takedown (No Duplicate)" : "This title has in-window avails, however offers are taken-down. Please validate if the take-down can be removed so that title is made available to customers.",
    "Offer Takedown (Duplicate)" : "This title has offers taken-down. However, there is another duplicate episode with live offers <insert episode details>. Since there is a live duplicate, requesting team to confirm if they can perform a tombstone or title take-down on the taken-down title to remove the unavailable duplicate from detail page. If take-down/tombstone is advised please route the ticket to appropriate CTI.",
    "TVOD Pricing Issue" : "This title has in-window avails and offers are created/approved. However, this is a TVOD offer and pricing is 0$ or n/a. Please review there is an issue due to pricing that is resulting in the title not being live/available to customers on storefront.",
    "Merge Issue (Duplicate Episode/Season)" : "There are multiple duplicate episodes <note duplicate episode sequence> in the season <note season sequence>. Merge team to review whether these duplicates can be merged.",
    "Page Not Found Error" : "When accessing Season X <insert season sequence> detail page via EDP we observe a page not found error. Please review if this can be corrected so that Season X is made available to customers.",
    "ASIN Leads To Different Page" : "When accessing Season X <insert season sequence> detail page via EDP it takes us to a detail page for season Y <insert season sequence>. Please review if this can be corrected so that Season X is made available to customers.",
    "Episode Missing Play Button" : "Episode X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing play button on site and helps us to resolve this issue.",
    "Episode Missing In PV Site" : "Episode X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing on site and helps us to resolve this issue.",
    "Episode Missing In EDP" : "Episode X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Season Missing Play Button" : "Season X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing play button on site and helps us to resolve this issue.",
    "Season Missing In PV site" : "Season X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing on site and helps us to resolve this issue.",
    "Season Missing In EDP" : "Season X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Episode/Season Missing Due To Repurposing" : "Episode/Season X is missing on PV site. While checking on EDP  we have live offers and avails but the publishing status is 'not ready' in CST due to <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers."
}

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"

# Field headers
fields = [
    "RCA", "Territory", "Offer Type", "Partner", "Season/Episode Number", 
    "EDP Link", "EDP Link 2", "D2C Link", "D2C Link 2",
    "Detail Page Link", "Detail Page Link 2", "Avails Link", "Avails Link 2", "DEAL Link", "WMV Link", "Screenshot", "Issue"
]

class TVSeriesForm(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Configure window
        self.title("TV Series Task Form")
        self.geometry("1400x900")
        self.state('zoomed')  # Maximize window on Windows
        
        # Initialize all instance variables
        self.entries = {}
        self.selected_issues = []
        self.selected_rca_items = []
        self.selected_offers = set()
        self.cti_values = ["", "", ""]  # Category, Type, Item
        self.excel_data = []
        self.task_data = []
        self.task_dialog = None
        self.task_entries = {}
        self.edp_link_url = ""
        self.current_gti_index = 0
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        self.start_time = None
        self.end_time = None
        self.break_start_time = None  # datetime.time object for tracking breaks internally
        self.break_end_time = None    # datetime.time object for tracking breaks internally
        self.total_break_time = 0.0   # Total break time in minutes (float)
        self.break_duration_db = 0.0   # Break duration saved to database
        self.issue_name = ""  # Store the button name separately
        self.form_counter = 1  # Track number of forms
        self.additional_forms = []  # Store additional form data
        self.active_form = 'main'  # Track which form is currently active
        self.cumulative_time = 0.0  # Store the cumulative total time for display
        self.titles_processed = 0  # Counter for completed/submitted titles
        self.autosave_path = os.path.join(os.path.expanduser("~"), ".tv_series_tool_autosave.pkl")
        
        # Season/Episode data for View Details functionality
        self.current_season_episode_data = ""
        self.current_territory_data = ""
        
        # Initialize UI
        self.setup_ui()

        # Register auto-save on exit
        atexit.register(self.auto_save_data)

        # Try to load previous session data
        self.try_load_autosave()

    def setup_ui(self):
        """Setup the main UI components"""
        # Create main container with padding
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create header frame
        self.create_header(main_frame)
        
        # Create scrollable content frame
        self.create_content(main_frame)
    
    def create_header(self, parent):
        """Create the header section with title and buttons"""
        header_frame = ctk.CTkFrame(parent, height=80)
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Title and status labels
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="left", fill="y", padx=20)
        
        title_label = ctk.CTkLabel(title_frame, text="TV Series Task Form:", 
                                 font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w")
        
        # Time and titles display
        status_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        status_frame.pack(anchor="w", pady=(5, 0))
        
        self.time_display = ctk.CTkLabel(status_frame, text="Total Time: 0.00 min",
                                       font=ctk.CTkFont(size=14))
        self.time_display.pack(side="left", padx=(0, 20))
        
        self.titles_display = ctk.CTkLabel(status_frame, text="Titles Processed: 0",
                                         font=ctk.CTkFont(size=14))
        self.titles_display.pack(side="left")
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        buttons_frame.pack(side="right", fill="y", padx=20)
        
        # Create buttons with modern styling
        button_configs = [
            ("Import", self.import_excel, "#1f538d"),
            ("Task", self.open_task_dialog, "#1f538d"),
            ("Get Task", self.get_next_task, "#7b2cbf"),
            ("Pause", self.set_break_mode, "#f77f00"),
            ("Play", self.set_active_mode, "#2d6a4f"),
            ("Submit", self.submit_task, "#1f538d")
        ]
        
        for i, (text, command, color) in enumerate(button_configs):
            btn = ctk.CTkButton(buttons_frame, text=text, command=command,
                              fg_color=color, hover_color=self.darken_color(color),
                              width=80, height=35)
            btn.pack(side="left", padx=5)
    
    def darken_color(self, color):
        """Darken a hex color for hover effect"""
        # Simple darkening by reducing each RGB component
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, int(c * 0.8)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def normalize_unicode_text(self, text):
        """Normalize Unicode text to handle international characters properly"""
        if not text:
            return ""
        try:
            # Convert to string first
            text_str = str(text)
            # Normalize Unicode text to NFC form for consistent character representation
            normalized = unicodedata.normalize('NFC', text_str)
            # Ensure proper encoding for web forms
            encoded = normalized.encode('utf-8').decode('utf-8')
            return encoded
        except Exception as e:
            print(f"Unicode normalization error: {e}")
            # Fallback to basic string conversion
            try:
                return str(text).encode('utf-8', errors='replace').decode('utf-8')
            except:
                return str(text)

    def create_content(self, parent):
        """Create the main content area with scrollable sections"""
        # Create scrollable frame
        scrollable_frame = ctk.CTkScrollableFrame(parent)
        scrollable_frame.pack(fill="both", expand=True)

        # Create sections
        self.create_task_section(scrollable_frame)
        self.create_comment_section(scrollable_frame)
        self.create_rca_section(scrollable_frame)
        self.create_form_fields_section(scrollable_frame)
        self.create_issue_blurbs_section(scrollable_frame)
        self.create_cti_section(scrollable_frame)

    def create_task_section(self, parent):
        """Create the Task section"""
        task_frame = ctk.CTkFrame(parent)
        task_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(task_frame, text="Task",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # Task fields
        task_fields = [
            "AHT", "Series GTI", "Series Name", "Line of business", "Territory (T)",
            "Partner (P)", "Impressions", "Season/Episode", "Input Date", "EDP Link"
        ]

        self.task_entries = {}
        for field in task_fields:
            self.create_field_row(task_frame, field, self.task_entries, is_task_field=True)

    def create_comment_section(self, parent):
        """Create the Comment section"""
        comment_frame = ctk.CTkFrame(parent)
        comment_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(comment_frame, text="Comment Section",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # Comment fields
        comment_fields = [
            "Code Expansion", "Parity Check", "POM Alias", "Issue Detail", "Action",
            "Resolver Group", "Additional Comments", "Prime", "TVOD", "Channel"
        ]

        for field in comment_fields:
            self.create_field_row(comment_frame, field, self.task_entries, is_comment_field=True)

    def create_field_row(self, parent, field_name, entries_dict, is_task_field=False, is_comment_field=False):
        """Create a field row with label and input"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", padx=20, pady=5)

        # Label
        label = ctk.CTkLabel(row_frame, text=field_name, width=200, anchor="w",
                           font=ctk.CTkFont(size=14, weight="bold"))
        label.pack(side="left", padx=(0, 20))

        # Input field based on field type
        if field_name == "Offer Type":
            # Dropdown button for Offer Type
            offer_btn = ctk.CTkButton(row_frame, text="Select Offer Type", width=300,
                                    command=self.open_offer_menu_click)
            offer_btn.pack(side="left")
            entries_dict[field_name] = offer_btn
        elif field_name == "Line of business":
            # Dropdown for Line of business
            lob_btn = ctk.CTkButton(row_frame, text="Select Line of Business", width=300,
                                  command=self.open_lob_menu)
            lob_btn.pack(side="left")
            entries_dict[field_name] = lob_btn
        elif field_name == "EDP Link":
            # Button for EDP Link
            link_btn = ctk.CTkButton(row_frame, text="Link", width=300,
                                   command=self.open_edp_link)
            link_btn.pack(side="left")
            entries_dict[field_name] = link_btn
        elif field_name == "Code Expansion":
            # Dropdown for Code Expansion
            code_btn = ctk.CTkButton(row_frame, text="Select Code Expansion", width=300,
                                   command=self.open_code_expansion_menu)
            code_btn.pack(side="left")
            entries_dict[field_name] = code_btn
        elif field_name == "Action":
            # Dropdown for Action
            action_btn = ctk.CTkButton(row_frame, text="Select Action", width=300,
                                     command=self.open_action_menu)
            action_btn.pack(side="left")
            entries_dict[field_name] = action_btn
        elif field_name == "Resolver Group":
            # Dropdown for Resolver Group
            resolver_btn = ctk.CTkButton(row_frame, text="Select Resolver Group", width=300,
                                       command=self.open_resolver_menu)
            resolver_btn.pack(side="left")
            entries_dict[field_name] = resolver_btn
        elif field_name == "Season/Episode" and is_task_field:
            # Special handling for Season/Episode with View Details button
            input_frame = ctk.CTkFrame(row_frame, fg_color="transparent")
            input_frame.pack(side="left", fill="x", expand=True)

            text_field = ctk.CTkTextbox(input_frame, height=80, width=400)
            text_field.pack(side="left", padx=(0, 10))
            entries_dict[field_name] = text_field

            view_btn = ctk.CTkButton(input_frame, text="View Details", width=120,
                                   command=self.view_season_episode_details)
            view_btn.pack(side="left")
        else:
            # Regular text field
            if field_name in ["RCA", "Issue", "Additional Comments", "Issue Detail"]:
                # Larger text area for these fields
                text_field = ctk.CTkTextbox(row_frame, height=100, width=600)
            else:
                # Standard text field
                text_field = ctk.CTkEntry(row_frame, width=600)
            text_field.pack(side="left")
            entries_dict[field_name] = text_field

    def create_rca_section(self, parent):
        """Create the RCA section with buttons"""
        rca_frame = ctk.CTkFrame(parent)
        rca_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(rca_frame, text="RCA",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # RCA buttons grid
        buttons_frame = ctk.CTkFrame(rca_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))

        # Create grid of RCA buttons
        row = 0
        col = 0
        max_cols = 4

        for rca_button, rca_text in rca_dict.items():
            btn = ctk.CTkButton(buttons_frame, text=rca_button,
                              command=lambda text=rca_text: self.set_rca_text(text),
                              width=250, height=35, fg_color="transparent",
                              text_color=("gray10", "gray90"), border_width=2)
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # Configure grid weights
        for i in range(max_cols):
            buttons_frame.grid_columnconfigure(i, weight=1)

        # Reset RCA button
        reset_btn = ctk.CTkButton(rca_frame, text="Reset RCA Selection",
                                fg_color="#dc2626", hover_color="#b91c1c",
                                command=self.reset_rca_selection)
        reset_btn.pack(pady=(0, 20))

    def create_form_fields_section(self, parent):
        """Create the main form fields section"""
        form_frame = ctk.CTkFrame(parent)
        form_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(form_frame, text="Form Fields",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # Form fields
        self.entries = {}
        for field in fields:
            self.create_field_row(form_frame, field, self.entries)

        # Add/Remove buttons
        buttons_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=20)

        add_btn = ctk.CTkButton(buttons_frame, text="Add +",
                              fg_color="#16a34a", hover_color="#15803d",
                              command=self.add_new_form, width=100)
        add_btn.pack(side="right", padx=(10, 0))

        remove_btn = ctk.CTkButton(buttons_frame, text="Remove -",
                                 fg_color="#dc2626", hover_color="#b91c1c",
                                 command=self.remove_form, width=100)
        remove_btn.pack(side="right")

    def create_issue_blurbs_section(self, parent):
        """Create the Issue Blurbs section"""
        blurbs_frame = ctk.CTkFrame(parent)
        blurbs_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(blurbs_frame, text="Issue Blurbs",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # Issue blurbs buttons grid
        buttons_frame = ctk.CTkFrame(blurbs_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))

        # Create grid of issue blurb buttons
        row = 0
        col = 0
        max_cols = 7

        for blurb, issue in blurbs_dict.items():
            btn = ctk.CTkButton(buttons_frame, text=blurb,
                              command=lambda i=issue, b=blurb: self.set_issue(i, b),
                              width=180, height=35, fg_color="transparent",
                              text_color=("gray10", "gray90"), border_width=2)
            btn.grid(row=row, column=col, padx=3, pady=3, sticky="ew")

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # Configure grid weights
        for i in range(max_cols):
            buttons_frame.grid_columnconfigure(i, weight=1)

        # Reset button
        reset_btn = ctk.CTkButton(blurbs_frame, text="Reset Selection",
                                fg_color="#dc2626", hover_color="#b91c1c",
                                command=self.reset_issue_selection)
        reset_btn.pack(pady=(0, 20))

    def create_cti_section(self, parent):
        """Create the CTI section"""
        cti_frame = ctk.CTkFrame(parent)
        cti_frame.pack(fill="x", pady=(0, 20))

        # Section title
        title_label = ctk.CTkLabel(cti_frame, text="CTI",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(anchor="w", padx=20, pady=(20, 10))

        # CTI buttons
        buttons_frame = ctk.CTkFrame(cti_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))

        cti_values = ["Digital Video", "Digiflex", "TV Series Integrity"]
        self.cti_buttons = []

        for i, value in enumerate(cti_values):
            btn = ctk.CTkButton(buttons_frame, text=value, width=200,
                              command=lambda idx=i: self.open_cti_menu(idx))
            btn.pack(side="left", padx=10)
            self.cti_buttons.append(btn)

    # Helper methods for UI interactions
    def set_rca_text(self, text):
        """Set RCA text in the form field"""
        if "RCA" in self.entries:
            if hasattr(self.entries["RCA"], 'delete'):
                # CTkTextbox
                self.entries["RCA"].delete("1.0", "end")
                self.entries["RCA"].insert("1.0", text)
            else:
                # CTkEntry
                self.entries["RCA"].delete(0, "end")
                self.entries["RCA"].insert(0, text)
        self.selected_rca_items = [text]

    def set_issue(self, issue_text, issue_name):
        """Set issue text in the form field"""
        if "Issue" in self.entries:
            if hasattr(self.entries["Issue"], 'delete'):
                # CTkTextbox
                self.entries["Issue"].delete("1.0", "end")
                self.entries["Issue"].insert("1.0", issue_text)
            else:
                # CTkEntry
                self.entries["Issue"].delete(0, "end")
                self.entries["Issue"].insert(0, issue_text)
        self.selected_issues = [issue_text]
        self.issue_name = issue_name

    def reset_rca_selection(self):
        """Reset RCA selection"""
        if "RCA" in self.entries:
            if hasattr(self.entries["RCA"], 'delete'):
                # CTkTextbox
                self.entries["RCA"].delete("1.0", "end")
            else:
                # CTkEntry
                self.entries["RCA"].delete(0, "end")
        self.selected_rca_items = []

    def reset_issue_selection(self):
        """Reset issue selection"""
        if "Issue" in self.entries:
            if hasattr(self.entries["Issue"], 'delete'):
                # CTkTextbox
                self.entries["Issue"].delete("1.0", "end")
            else:
                # CTkEntry
                self.entries["Issue"].delete(0, "end")
        self.selected_issues = []
        self.issue_name = ""

    def open_cti_menu(self, index):
        """Open CTI menu for specific index"""
        cti_options = ["Digital Video", "Digiflex", "TV Series Integrity"]
        specific_option = cti_options[index]
        self.cti_values[index] = specific_option
        if index < len(self.cti_buttons):
            self.cti_buttons[index].configure(text=specific_option)

    # Dropdown menu methods (simplified for CustomTkinter)
    def open_offer_menu_click(self):
        """Open offer type selection dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("Select Offer Types")
        dialog.geometry("400x300")
        dialog.transient(self)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")

        # Create checkboxes for offer types
        offer_types = ["Prime", "TVOD", "Channels", "FVOD", "AVOD", "POEST", "SVOD"]
        checkboxes = {}

        for offer_type in offer_types:
            var = tk.BooleanVar(value=offer_type in self.selected_offers)
            cb = ctk.CTkCheckBox(dialog, text=offer_type, variable=var)
            cb.pack(pady=5, padx=20, anchor="w")
            checkboxes[offer_type] = var

        def apply_selection():
            self.selected_offers.clear()
            for offer_type, var in checkboxes.items():
                if var.get():
                    self.selected_offers.add(offer_type)

            # Update button text
            if self.selected_offers:
                self.entries["Offer Type"].configure(text=", ".join(sorted(self.selected_offers)))
            else:
                self.entries["Offer Type"].configure(text="Select Offer Type")
            dialog.destroy()

        # Buttons
        btn_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        btn_frame.pack(side="bottom", fill="x", padx=20, pady=20)

        ctk.CTkButton(btn_frame, text="Apply", command=apply_selection).pack(side="right", padx=(10, 0))
        ctk.CTkButton(btn_frame, text="Cancel", command=dialog.destroy).pack(side="right")

    def open_lob_menu(self):
        """Open Line of Business menu"""
        lob_options = ["Prime Video", "TVOD", "Channels", "Other"]
        self.show_selection_dialog("Line of Business", lob_options, self.task_entries["Line of business"])

    def open_code_expansion_menu(self):
        """Open Code Expansion menu"""
        code_options = ["Code 1", "Code 2", "Code 3", "Other"]
        self.show_selection_dialog("Code Expansion", code_options, self.task_entries["Code Expansion"])

    def open_action_menu(self):
        """Open Action menu"""
        action_options = ["Action 1", "Action 2", "Action 3", "Other"]
        self.show_selection_dialog("Action", action_options, self.task_entries["Action"])

    def open_resolver_menu(self):
        """Open Resolver Group menu"""
        resolver_options = ["Group 1", "Group 2", "Group 3", "Other"]
        self.show_selection_dialog("Resolver Group", resolver_options, self.task_entries["Resolver Group"])

    def show_selection_dialog(self, title, options, target_button):
        """Show a selection dialog for dropdown options"""
        dialog = ctk.CTkToplevel(self)
        dialog.title(f"Select {title}")
        dialog.geometry("300x400")
        dialog.transient(self)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"300x400+{x}+{y}")

        # Create radio buttons for options
        selected_var = tk.StringVar()

        for option in options:
            rb = ctk.CTkRadioButton(dialog, text=option, variable=selected_var, value=option)
            rb.pack(pady=5, padx=20, anchor="w")

        def apply_selection():
            if selected_var.get():
                target_button.configure(text=selected_var.get())
            dialog.destroy()

        # Buttons
        btn_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        btn_frame.pack(side="bottom", fill="x", padx=20, pady=20)

        ctk.CTkButton(btn_frame, text="Apply", command=apply_selection).pack(side="right", padx=(10, 0))
        ctk.CTkButton(btn_frame, text="Cancel", command=dialog.destroy).pack(side="right")

    def open_edp_link(self):
        """Open EDP Link"""
        if self.edp_link_url:
            import webbrowser
            webbrowser.open(self.edp_link_url)
        else:
            messagebox.showinfo("Info", "No EDP Link available")

    def view_season_episode_details(self):
        """Show Season/Episode details when View Details button is clicked"""
        # Get the Season/Episode data from the task entries
        if "Season/Episode" in self.task_entries:
            text_widget = self.task_entries["Season/Episode"]
            if hasattr(text_widget, 'get'):
                # CTkTextbox
                season_episode_data = text_widget.get("1.0", "end-1c")
            else:
                # CTkEntry
                season_episode_data = text_widget.get()
        else:
            season_episode_data = ""

        territory_data = ""
        if "Territory (T)" in self.task_entries:
            territory_widget = self.task_entries["Territory (T)"]
            if hasattr(territory_widget, 'get'):
                if callable(territory_widget.get):
                    try:
                        territory_data = territory_widget.get("1.0", "end-1c")
                    except:
                        territory_data = territory_widget.get()
                else:
                    territory_data = str(territory_widget.get)

        if season_episode_data:
            self.show_season_episode_dialog(season_episode_data, territory_data)
        else:
            messagebox.showinfo("No Data", "No Season/Episode data available to display.")

    # Placeholder methods for functionality that needs to be implemented
    def import_excel(self):
        """Import Excel file"""
        messagebox.showinfo("Info", "Import Excel functionality to be implemented")

    def open_task_dialog(self):
        """Open task dialog"""
        messagebox.showinfo("Info", "Task dialog functionality to be implemented")

    def get_next_task(self):
        """Get next task"""
        messagebox.showinfo("Info", "Get next task functionality to be implemented")

    def set_break_mode(self):
        """Set break mode"""
        messagebox.showinfo("Info", "Break mode functionality to be implemented")

    def set_active_mode(self):
        """Set active mode"""
        messagebox.showinfo("Info", "Active mode functionality to be implemented")

    def submit_task(self):
        """Submit task"""
        messagebox.showinfo("Info", "Submit task functionality to be implemented")

    def add_new_form(self):
        """Add new form"""
        messagebox.showinfo("Info", "Add new form functionality to be implemented")

    def remove_form(self):
        """Remove form"""
        messagebox.showinfo("Info", "Remove form functionality to be implemented")

    def show_season_episode_dialog(self, season_episode_data, territory_data):
        """Show season/episode data in a dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("Season/Episode Data")
        dialog.geometry("800x600")
        dialog.transient(self)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"800x600+{x}+{y}")

        # Create scrollable text area
        text_area = ctk.CTkTextbox(dialog, wrap="word")
        text_area.pack(fill="both", expand=True, padx=20, pady=20)

        # Insert the data
        display_text = f"Season/Episode Data:\n{season_episode_data}\n\nTerritory Data:\n{territory_data}"
        text_area.insert("1.0", display_text)
        text_area.configure(state="disabled")

        # Close button
        close_btn = ctk.CTkButton(dialog, text="Close", command=dialog.destroy)
        close_btn.pack(pady=(0, 20))

    def auto_save_data(self):
        """Auto-save data"""
        try:
            # Implement auto-save functionality
            pass
        except Exception as e:
            print(f"Auto-save error: {e}")

    def try_load_autosave(self):
        """Try to load auto-saved data"""
        try:
            # Implement auto-load functionality
            pass
        except Exception as e:
            print(f"Auto-load error: {e}")

if __name__ == "__main__":
    try:
        app = TVSeriesForm()
        app.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")
