('C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\Ticket '
 'Process\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\doctest.py',
   'PYMODULE'),
  ('docutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\__init__.py',
   'PYMODULE'),
  ('docutils.frontend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\frontend.py',
   'PYMODULE'),
  ('docutils.io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\io.py',
   'PYMODULE'),
  ('docutils.languages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.languages.af',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\af.py',
   'PYMODULE'),
  ('docutils.languages.ar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ar.py',
   'PYMODULE'),
  ('docutils.languages.ca',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.languages.cs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.languages.da',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\da.py',
   'PYMODULE'),
  ('docutils.languages.de',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\de.py',
   'PYMODULE'),
  ('docutils.languages.en',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\en.py',
   'PYMODULE'),
  ('docutils.languages.eo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.languages.es',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\es.py',
   'PYMODULE'),
  ('docutils.languages.fa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.languages.fi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.languages.fr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.languages.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.languages.he',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\he.py',
   'PYMODULE'),
  ('docutils.languages.it',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\it.py',
   'PYMODULE'),
  ('docutils.languages.ja',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.languages.ka',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ka.py',
   'PYMODULE'),
  ('docutils.languages.ko',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.languages.lt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.languages.lv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.languages.nl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.languages.pl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.languages.pt_br',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.languages.ru',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.languages.sk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.languages.sv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.languages.uk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\uk.py',
   'PYMODULE'),
  ('docutils.languages.zh_cn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.languages.zh_tw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.nodes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\nodes.py',
   'PYMODULE'),
  ('docutils.parsers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.admonitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\admonitions.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.body',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\body.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\html.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.images',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\images.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\misc.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.parts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\parts.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.references',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\references.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.tables',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\tables.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.af',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\af.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ar.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ca',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.cs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.da',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\da.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.de',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\de.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.en',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\en.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.eo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.es',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\es.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.he',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\he.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.it',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\it.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ja',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ka',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ka.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ko',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.nl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pt_br',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ru',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.uk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\uk.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_cn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_tw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.parsers.rst.roles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\roles.py',
   'PYMODULE'),
  ('docutils.parsers.rst.states',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\states.py',
   'PYMODULE'),
  ('docutils.parsers.rst.tableparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\tableparser.py',
   'PYMODULE'),
  ('docutils.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\readers\\__init__.py',
   'PYMODULE'),
  ('docutils.readers.standalone',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\readers\\standalone.py',
   'PYMODULE'),
  ('docutils.statemachine',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\statemachine.py',
   'PYMODULE'),
  ('docutils.transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\__init__.py',
   'PYMODULE'),
  ('docutils.transforms.frontmatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\frontmatter.py',
   'PYMODULE'),
  ('docutils.transforms.misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\misc.py',
   'PYMODULE'),
  ('docutils.transforms.parts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\parts.py',
   'PYMODULE'),
  ('docutils.transforms.references',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\references.py',
   'PYMODULE'),
  ('docutils.transforms.universal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\universal.py',
   'PYMODULE'),
  ('docutils.transforms.writer_aux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\writer_aux.py',
   'PYMODULE'),
  ('docutils.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.code_analyzer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\code_analyzer.py',
   'PYMODULE'),
  ('docutils.utils.math',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.math.latex2mathml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\latex2mathml.py',
   'PYMODULE'),
  ('docutils.utils.math.math2html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\math2html.py',
   'PYMODULE'),
  ('docutils.utils.math.mathalphabet2unichar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\mathalphabet2unichar.py',
   'PYMODULE'),
  ('docutils.utils.math.mathml_elements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\mathml_elements.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2mathml_extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\tex2mathml_extern.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2unichar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\tex2unichar.py',
   'PYMODULE'),
  ('docutils.utils.math.unichar2tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\unichar2tex.py',
   'PYMODULE'),
  ('docutils.utils.punctuation_chars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\punctuation_chars.py',
   'PYMODULE'),
  ('docutils.utils.roman',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\roman.py',
   'PYMODULE'),
  ('docutils.utils.smartquotes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\smartquotes.py',
   'PYMODULE'),
  ('docutils.utils.urischemes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\urischemes.py',
   'PYMODULE'),
  ('docutils.writers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\__init__.py',
   'PYMODULE'),
  ('docutils.writers._html_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\_html_base.py',
   'PYMODULE'),
  ('docutils.writers.docutils_xml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\docutils_xml.py',
   'PYMODULE'),
  ('docutils.writers.html4css1',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html4css1\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.html5_polyglot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.latex2e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.manpage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\manpage.py',
   'PYMODULE'),
  ('docutils.writers.null',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\null.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt.prepstyles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\prepstyles.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt.pygmentsformatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\pygmentsformatter.py',
   'PYMODULE'),
  ('docutils.writers.pep_html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pep_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.pseudoxml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pseudoxml.py',
   'PYMODULE'),
  ('docutils.writers.s5_html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.xetex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\xetex\\__init__.py',
   'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fileinput.py',
   'PYMODULE'),
  ('filetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\__init__.py',
   'PYMODULE'),
  ('filetype.filetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\filetype.py',
   'PYMODULE'),
  ('filetype.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\helpers.py',
   'PYMODULE'),
  ('filetype.match',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\match.py',
   'PYMODULE'),
  ('filetype.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\__init__.py',
   'PYMODULE'),
  ('filetype.types.application',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\application.py',
   'PYMODULE'),
  ('filetype.types.archive',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\archive.py',
   'PYMODULE'),
  ('filetype.types.audio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\audio.py',
   'PYMODULE'),
  ('filetype.types.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\base.py',
   'PYMODULE'),
  ('filetype.types.document',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\document.py',
   'PYMODULE'),
  ('filetype.types.font',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\font.py',
   'PYMODULE'),
  ('filetype.types.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\image.py',
   'PYMODULE'),
  ('filetype.types.isobmff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\isobmff.py',
   'PYMODULE'),
  ('filetype.types.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\video.py',
   'PYMODULE'),
  ('filetype.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kivy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\__init__.py',
   'PYMODULE'),
  ('kivy._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\_version.py',
   'PYMODULE'),
  ('kivy.animation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\animation.py',
   'PYMODULE'),
  ('kivy.app',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\app.py',
   'PYMODULE'),
  ('kivy.atlas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\atlas.py',
   'PYMODULE'),
  ('kivy.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\base.py',
   'PYMODULE'),
  ('kivy.cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\cache.py',
   'PYMODULE'),
  ('kivy.clock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\clock.py',
   'PYMODULE'),
  ('kivy.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\compat.py',
   'PYMODULE'),
  ('kivy.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\config.py',
   'PYMODULE'),
  ('kivy.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\context.py',
   'PYMODULE'),
  ('kivy.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\__init__.py',
   'PYMODULE'),
  ('kivy.core.audio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\__init__.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_android.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_avplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_avplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_gstplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_pygame.py',
   'PYMODULE'),
  ('kivy.core.camera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\__init__.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_android.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_gi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_gi.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_opencv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_opencv.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_picamera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_picamera.py',
   'PYMODULE'),
  ('kivy.core.clipboard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\__init__.py',
   'PYMODULE'),
  ('kivy.core.clipboard._clipboard_ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\_clipboard_ext.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_android.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_dbusklipper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_dbusklipper.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_dummy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_dummy.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_gtk3',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_gtk3.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_nspaste',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_nspaste.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_pygame.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_sdl2.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_winctypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_winctypes.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_xclip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_xclip.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_xsel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_xsel.py',
   'PYMODULE'),
  ('kivy.core.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\gl\\__init__.py',
   'PYMODULE'),
  ('kivy.core.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\__init__.py',
   'PYMODULE'),
  ('kivy.core.image.img_dds',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_dds.py',
   'PYMODULE'),
  ('kivy.core.image.img_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.image.img_pil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_pil.py',
   'PYMODULE'),
  ('kivy.core.image.img_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_pygame.py',
   'PYMODULE'),
  ('kivy.core.image.img_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_sdl2.py',
   'PYMODULE'),
  ('kivy.core.image.img_tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_tex.py',
   'PYMODULE'),
  ('kivy.core.spelling',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\__init__.py',
   'PYMODULE'),
  ('kivy.core.spelling.spelling_enchant',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\spelling_enchant.py',
   'PYMODULE'),
  ('kivy.core.spelling.spelling_osxappkit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\spelling_osxappkit.py',
   'PYMODULE'),
  ('kivy.core.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\__init__.py',
   'PYMODULE'),
  ('kivy.core.text.markup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\markup.py',
   'PYMODULE'),
  ('kivy.core.text.text_pango',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pango.py',
   'PYMODULE'),
  ('kivy.core.text.text_pil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pil.py',
   'PYMODULE'),
  ('kivy.core.text.text_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pygame.py',
   'PYMODULE'),
  ('kivy.core.text.text_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_sdl2.py',
   'PYMODULE'),
  ('kivy.core.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\__init__.py',
   'PYMODULE'),
  ('kivy.core.video.video_ffmpeg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_ffmpeg.py',
   'PYMODULE'),
  ('kivy.core.video.video_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.video.video_gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_gstplayer.py',
   'PYMODULE'),
  ('kivy.core.video.video_null',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_null.py',
   'PYMODULE'),
  ('kivy.core.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\__init__.py',
   'PYMODULE'),
  ('kivy.core.window.window_egl_rpi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_egl_rpi.py',
   'PYMODULE'),
  ('kivy.core.window.window_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_pygame.py',
   'PYMODULE'),
  ('kivy.core.window.window_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_sdl2.py',
   'PYMODULE'),
  ('kivy.deps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\deps\\__init__.py',
   'PYMODULE'),
  ('kivy.effects',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\__init__.py',
   'PYMODULE'),
  ('kivy.effects.dampedscroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\dampedscroll.py',
   'PYMODULE'),
  ('kivy.effects.kinetic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\kinetic.py',
   'PYMODULE'),
  ('kivy.effects.opacityscroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\opacityscroll.py',
   'PYMODULE'),
  ('kivy.effects.scroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\scroll.py',
   'PYMODULE'),
  ('kivy.event',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\event.py',
   'PYMODULE'),
  ('kivy.eventmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\eventmanager\\__init__.py',
   'PYMODULE'),
  ('kivy.extras',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\extras\\__init__.py',
   'PYMODULE'),
  ('kivy.extras.highlight',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\extras\\highlight.py',
   'PYMODULE'),
  ('kivy.factory',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\factory.py',
   'PYMODULE'),
  ('kivy.factory_registers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\factory_registers.py',
   'PYMODULE'),
  ('kivy.gesture',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\gesture.py',
   'PYMODULE'),
  ('kivy.graphics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\__init__.py',
   'PYMODULE'),
  ('kivy.graphics.cgl_backend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\__init__.py',
   'PYMODULE'),
  ('kivy.input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\__init__.py',
   'PYMODULE'),
  ('kivy.input.factory',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\factory.py',
   'PYMODULE'),
  ('kivy.input.motionevent',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\motionevent.py',
   'PYMODULE'),
  ('kivy.input.postproc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\__init__.py',
   'PYMODULE'),
  ('kivy.input.postproc.calibration',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\calibration.py',
   'PYMODULE'),
  ('kivy.input.postproc.dejitter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\dejitter.py',
   'PYMODULE'),
  ('kivy.input.postproc.doubletap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\doubletap.py',
   'PYMODULE'),
  ('kivy.input.postproc.ignorelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\ignorelist.py',
   'PYMODULE'),
  ('kivy.input.postproc.retaintouch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\retaintouch.py',
   'PYMODULE'),
  ('kivy.input.postproc.tripletap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\tripletap.py',
   'PYMODULE'),
  ('kivy.input.provider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\provider.py',
   'PYMODULE'),
  ('kivy.input.providers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\__init__.py',
   'PYMODULE'),
  ('kivy.input.providers.androidjoystick',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\androidjoystick.py',
   'PYMODULE'),
  ('kivy.input.providers.hidinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\hidinput.py',
   'PYMODULE'),
  ('kivy.input.providers.leapfinger',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\leapfinger.py',
   'PYMODULE'),
  ('kivy.input.providers.linuxwacom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\linuxwacom.py',
   'PYMODULE'),
  ('kivy.input.providers.mactouch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mactouch.py',
   'PYMODULE'),
  ('kivy.input.providers.mouse',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mouse.py',
   'PYMODULE'),
  ('kivy.input.providers.mtdev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mtdev.py',
   'PYMODULE'),
  ('kivy.input.providers.probesysfs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\probesysfs.py',
   'PYMODULE'),
  ('kivy.input.providers.tuio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\tuio.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_common.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_pen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_pen.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_touch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_touch.py',
   'PYMODULE'),
  ('kivy.input.shape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\shape.py',
   'PYMODULE'),
  ('kivy.lang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\__init__.py',
   'PYMODULE'),
  ('kivy.lang.builder',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\builder.py',
   'PYMODULE'),
  ('kivy.lang.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\parser.py',
   'PYMODULE'),
  ('kivy.lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\__init__.py',
   'PYMODULE'),
  ('kivy.lib.ddsfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\ddsfile.py',
   'PYMODULE'),
  ('kivy.lib.gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\gstplayer\\__init__.py',
   'PYMODULE'),
  ('kivy.lib.mtdev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\mtdev.py',
   'PYMODULE'),
  ('kivy.lib.vidcore_lite',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\vidcore_lite\\__init__.py',
   'PYMODULE'),
  ('kivy.loader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\loader.py',
   'PYMODULE'),
  ('kivy.logger',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\logger.py',
   'PYMODULE'),
  ('kivy.metrics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\metrics.py',
   'PYMODULE'),
  ('kivy.modules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__init__.py',
   'PYMODULE'),
  ('kivy.multistroke',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\multistroke.py',
   'PYMODULE'),
  ('kivy.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\parser.py',
   'PYMODULE'),
  ('kivy.resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\resources.py',
   'PYMODULE'),
  ('kivy.setupconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\setupconfig.py',
   'PYMODULE'),
  ('kivy.support',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\support.py',
   'PYMODULE'),
  ('kivy.uix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.accordion',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\accordion.py',
   'PYMODULE'),
  ('kivy.uix.actionbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\actionbar.py',
   'PYMODULE'),
  ('kivy.uix.anchorlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\anchorlayout.py',
   'PYMODULE'),
  ('kivy.uix.behaviors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\button.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.codenavigation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\codenavigation.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.compoundselection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\compoundselection.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.cover',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\cover.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.drag',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\drag.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.emacs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\emacs.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.focus',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\focus.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.knspace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\knspace.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.togglebutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\togglebutton.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.touchripple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\touchripple.py',
   'PYMODULE'),
  ('kivy.uix.boxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\boxlayout.py',
   'PYMODULE'),
  ('kivy.uix.bubble',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\bubble.py',
   'PYMODULE'),
  ('kivy.uix.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\button.py',
   'PYMODULE'),
  ('kivy.uix.camera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\camera.py',
   'PYMODULE'),
  ('kivy.uix.carousel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\carousel.py',
   'PYMODULE'),
  ('kivy.uix.checkbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\checkbox.py',
   'PYMODULE'),
  ('kivy.uix.codeinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\codeinput.py',
   'PYMODULE'),
  ('kivy.uix.colorpicker',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\colorpicker.py',
   'PYMODULE'),
  ('kivy.uix.dropdown',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\dropdown.py',
   'PYMODULE'),
  ('kivy.uix.effectwidget',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\effectwidget.py',
   'PYMODULE'),
  ('kivy.uix.filechooser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\filechooser.py',
   'PYMODULE'),
  ('kivy.uix.floatlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\floatlayout.py',
   'PYMODULE'),
  ('kivy.uix.gesturesurface',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\gesturesurface.py',
   'PYMODULE'),
  ('kivy.uix.gridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\gridlayout.py',
   'PYMODULE'),
  ('kivy.uix.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\image.py',
   'PYMODULE'),
  ('kivy.uix.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\label.py',
   'PYMODULE'),
  ('kivy.uix.layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\layout.py',
   'PYMODULE'),
  ('kivy.uix.modalview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\modalview.py',
   'PYMODULE'),
  ('kivy.uix.pagelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\pagelayout.py',
   'PYMODULE'),
  ('kivy.uix.popup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\popup.py',
   'PYMODULE'),
  ('kivy.uix.progressbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\progressbar.py',
   'PYMODULE'),
  ('kivy.uix.recycleboxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleboxlayout.py',
   'PYMODULE'),
  ('kivy.uix.recyclegridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recyclegridlayout.py',
   'PYMODULE'),
  ('kivy.uix.recyclelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recyclelayout.py',
   'PYMODULE'),
  ('kivy.uix.recycleview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.__init__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.datamodel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\datamodel.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\layout.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.views',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\views.py',
   'PYMODULE'),
  ('kivy.uix.relativelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\relativelayout.py',
   'PYMODULE'),
  ('kivy.uix.rst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\rst.py',
   'PYMODULE'),
  ('kivy.uix.sandbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\sandbox.py',
   'PYMODULE'),
  ('kivy.uix.scatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scatter.py',
   'PYMODULE'),
  ('kivy.uix.scatterlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scatterlayout.py',
   'PYMODULE'),
  ('kivy.uix.screenmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\screenmanager.py',
   'PYMODULE'),
  ('kivy.uix.scrollview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scrollview.py',
   'PYMODULE'),
  ('kivy.uix.settings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\settings.py',
   'PYMODULE'),
  ('kivy.uix.slider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\slider.py',
   'PYMODULE'),
  ('kivy.uix.spinner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\spinner.py',
   'PYMODULE'),
  ('kivy.uix.splitter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\splitter.py',
   'PYMODULE'),
  ('kivy.uix.stacklayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\stacklayout.py',
   'PYMODULE'),
  ('kivy.uix.stencilview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\stencilview.py',
   'PYMODULE'),
  ('kivy.uix.switch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\switch.py',
   'PYMODULE'),
  ('kivy.uix.tabbedpanel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\tabbedpanel.py',
   'PYMODULE'),
  ('kivy.uix.textinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\textinput.py',
   'PYMODULE'),
  ('kivy.uix.togglebutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\togglebutton.py',
   'PYMODULE'),
  ('kivy.uix.treeview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\treeview.py',
   'PYMODULE'),
  ('kivy.uix.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\video.py',
   'PYMODULE'),
  ('kivy.uix.videoplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\videoplayer.py',
   'PYMODULE'),
  ('kivy.uix.vkeyboard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\vkeyboard.py',
   'PYMODULE'),
  ('kivy.uix.widget',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\widget.py',
   'PYMODULE'),
  ('kivy.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\utils.py',
   'PYMODULE'),
  ('kivy.vector',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\vector.py',
   'PYMODULE'),
  ('kivy.weakmethod',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\weakmethod.py',
   'PYMODULE'),
  ('kivy_deps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy_deps\\__init__.py',
   'PYMODULE'),
  ('kivymd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\__init__.py',
   'PYMODULE'),
  ('kivymd._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\_version.py',
   'PYMODULE'),
  ('kivymd.app',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\app.py',
   'PYMODULE'),
  ('kivymd.color_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\color_definitions.py',
   'PYMODULE'),
  ('kivymd.factory_registers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\factory_registers.py',
   'PYMODULE'),
  ('kivymd.font_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\font_definitions.py',
   'PYMODULE'),
  ('kivymd.icon_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\icon_definitions.py',
   'PYMODULE'),
  ('kivymd.material_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\material_resources.py',
   'PYMODULE'),
  ('kivymd.theming',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\theming.py',
   'PYMODULE'),
  ('kivymd.theming_dynamic_text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\theming_dynamic_text.py',
   'PYMODULE'),
  ('kivymd.tools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\__init__.py',
   'PYMODULE'),
  ('kivymd.tools.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\packaging\\__init__.py',
   'PYMODULE'),
  ('kivymd.tools.packaging.pyinstaller',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\packaging\\pyinstaller\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.anchorlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\anchorlayout.py',
   'PYMODULE'),
  ('kivymd.uix.backdrop',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\backdrop\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.backdrop.backdrop',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\backdrop\\backdrop.py',
   'PYMODULE'),
  ('kivymd.uix.banner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\banner\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.banner.banner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\banner\\banner.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.backgroundcolor_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\backgroundcolor_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.declarative_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\declarative_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.elevation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\elevation.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.focus_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\focus_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.hover_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\hover_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.magic_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\magic_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.motion_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\motion_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.ripple_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\ripple_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.rotate_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\rotate_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.scale_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\scale_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.stencil_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\stencil_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.toggle_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\toggle_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.touch_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\touch_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.bottomnavigation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomnavigation\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.bottomnavigation.bottomnavigation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomnavigation\\bottomnavigation.py',
   'PYMODULE'),
  ('kivymd.uix.bottomsheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomsheet\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.bottomsheet.bottomsheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\bottomsheet\\bottomsheet.py',
   'PYMODULE'),
  ('kivymd.uix.boxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\boxlayout.py',
   'PYMODULE'),
  ('kivymd.uix.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\button\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.button.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\button\\button.py',
   'PYMODULE'),
  ('kivymd.uix.card',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\card\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.card.card',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\card\\card.py',
   'PYMODULE'),
  ('kivymd.uix.carousel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\carousel.py',
   'PYMODULE'),
  ('kivymd.uix.chip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\chip\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.chip.chip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\chip\\chip.py',
   'PYMODULE'),
  ('kivymd.uix.circularlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\circularlayout.py',
   'PYMODULE'),
  ('kivymd.uix.controllers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\controllers\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.controllers.windowcontroller',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\controllers\\windowcontroller.py',
   'PYMODULE'),
  ('kivymd.uix.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dialog\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.dialog.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dialog\\dialog.py',
   'PYMODULE'),
  ('kivymd.uix.dropdownitem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dropdownitem\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.dropdownitem.dropdownitem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dropdownitem\\dropdownitem.py',
   'PYMODULE'),
  ('kivymd.uix.expansionpanel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\expansionpanel\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.expansionpanel.expansionpanel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\expansionpanel\\expansionpanel.py',
   'PYMODULE'),
  ('kivymd.uix.fitimage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\fitimage\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.fitimage.fitimage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\fitimage\\fitimage.py',
   'PYMODULE'),
  ('kivymd.uix.floatlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\floatlayout.py',
   'PYMODULE'),
  ('kivymd.uix.gridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\gridlayout.py',
   'PYMODULE'),
  ('kivymd.uix.hero',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\hero.py',
   'PYMODULE'),
  ('kivymd.uix.imagelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\imagelist\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.imagelist.imagelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\imagelist\\imagelist.py',
   'PYMODULE'),
  ('kivymd.uix.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\label\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.label.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\label\\label.py',
   'PYMODULE'),
  ('kivymd.uix.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\list\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.list.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\list\\list.py',
   'PYMODULE'),
  ('kivymd.uix.menu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\menu\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.menu.menu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\menu\\menu.py',
   'PYMODULE'),
  ('kivymd.uix.navigationdrawer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationdrawer\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.navigationdrawer.navigationdrawer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationdrawer\\navigationdrawer.py',
   'PYMODULE'),
  ('kivymd.uix.navigationrail',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationrail\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.navigationrail.navigationrail',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\navigationrail\\navigationrail.py',
   'PYMODULE'),
  ('kivymd.uix.progressbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\progressbar\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.progressbar.progressbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\progressbar\\progressbar.py',
   'PYMODULE'),
  ('kivymd.uix.recyclegridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\recyclegridlayout.py',
   'PYMODULE'),
  ('kivymd.uix.recycleview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\recycleview.py',
   'PYMODULE'),
  ('kivymd.uix.refreshlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\refreshlayout\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.refreshlayout.refreshlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\refreshlayout\\refreshlayout.py',
   'PYMODULE'),
  ('kivymd.uix.relativelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\relativelayout.py',
   'PYMODULE'),
  ('kivymd.uix.responsivelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\responsivelayout.py',
   'PYMODULE'),
  ('kivymd.uix.screen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\screen.py',
   'PYMODULE'),
  ('kivymd.uix.screenmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\screenmanager.py',
   'PYMODULE'),
  ('kivymd.uix.scrollview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\scrollview.py',
   'PYMODULE'),
  ('kivymd.uix.segmentedbutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedbutton\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.segmentedbutton.segmentedbutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedbutton\\segmentedbutton.py',
   'PYMODULE'),
  ('kivymd.uix.segmentedcontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedcontrol\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.segmentedcontrol.segmentedcontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\segmentedcontrol\\segmentedcontrol.py',
   'PYMODULE'),
  ('kivymd.uix.selection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selection\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.selection.selection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selection\\selection.py',
   'PYMODULE'),
  ('kivymd.uix.selectioncontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selectioncontrol\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.selectioncontrol.selectioncontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selectioncontrol\\selectioncontrol.py',
   'PYMODULE'),
  ('kivymd.uix.slider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\slider\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.slider.slider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\slider\\slider.py',
   'PYMODULE'),
  ('kivymd.uix.sliverappbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\sliverappbar\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.sliverappbar.sliverappbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\sliverappbar\\sliverappbar.py',
   'PYMODULE'),
  ('kivymd.uix.spinner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\spinner\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.spinner.spinner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\spinner\\spinner.py',
   'PYMODULE'),
  ('kivymd.uix.stacklayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\stacklayout.py',
   'PYMODULE'),
  ('kivymd.uix.swiper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\swiper\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.swiper.swiper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\swiper\\swiper.py',
   'PYMODULE'),
  ('kivymd.uix.tab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tab\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.tab.tab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tab\\tab.py',
   'PYMODULE'),
  ('kivymd.uix.textfield',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\textfield\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.textfield.textfield',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\textfield\\textfield.py',
   'PYMODULE'),
  ('kivymd.uix.toolbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\toolbar\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.toolbar.toolbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\toolbar\\toolbar.py',
   'PYMODULE'),
  ('kivymd.uix.tooltip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tooltip\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.tooltip.tooltip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tooltip\\tooltip.py',
   'PYMODULE'),
  ('kivymd.uix.transition',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\transition\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.transition.transition',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\transition\\transition.py',
   'PYMODULE'),
  ('kivymd.uix.widget',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\widget.py',
   'PYMODULE'),
  ('kivymd.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\__init__.py',
   'PYMODULE'),
  ('kivymd.utils.asynckivy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\asynckivy.py',
   'PYMODULE'),
  ('kivymd.utils.fpsmonitor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\fpsmonitor.py',
   'PYMODULE'),
  ('kivymd.utils.set_bars_colors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\set_bars_colors.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\optparse.py',
   'PYMODULE'),
  ('outcome',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.storage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\storage.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.webextension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\webextension.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\site.py',
   'PYMODULE'),
  ('sniffio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('trio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('webdriver_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('win32com',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
